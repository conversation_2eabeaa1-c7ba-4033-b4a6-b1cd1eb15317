<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang-key="page.title">网红营销智能体 - AI驱动的网红建联管家</title>
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --dark-bg: #0f0f1e;
            --card-bg: rgba(26, 27, 46, 0.8);
            --accent-purple: #7c3aed;
            --accent-blue: #4f46e5;
            --text-primary: #ffffff;
            --text-secondary: #a5a6b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* 动态背景效果 */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(80px);
            opacity: 0.3;
            animation: float 20s infinite ease-in-out;
        }

        .orb-1 {
            width: 600px;
            height: 600px;
            background: var(--accent-purple);
            top: -300px;
            right: -200px;
        }

        .orb-2 {
            width: 400px;
            height: 400px;
            background: var(--accent-blue);
            bottom: -200px;
            left: -100px;
            animation-delay: -5s;
        }

        .orb-3 {
            width: 500px;
            height: 500px;
            background: #ec4899;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: -10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(15, 15, 30, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            padding: 0.5rem 0;
            background: rgba(15, 15, 30, 0.98);
        }

        /* 按钮样式 */
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            padding: 12px 28px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(124, 58, 237, 0.4);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* Hero Section 动画 */
        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            line-height: 1.2;
            background: linear-gradient(to right, #ffffff, #a5a6b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 卡片样式 */
        .glass-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-5px);
            border-color: rgba(124, 58, 237, 0.5);
            box-shadow: 0 20px 40px rgba(124, 58, 237, 0.2);
        }

        /* 功能图标 */
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            opacity: 0.2;
            z-index: -1;
        }

        /* 数据展示动画 */
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 工作流程线条 */
        .workflow-line {
            position: absolute;
            top: 30px;
            left: 30px;
            right: 30px;
            height: 2px;
            background: linear-gradient(to right, transparent, var(--accent-purple), transparent);
            z-index: -1;
        }

        /* 价格卡片 */
        .pricing-card {
            position: relative;
            overflow: hidden;
        }

        .pricing-card.featured {
            border: 2px solid var(--accent-purple);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: '最受欢迎';
            position: absolute;
            top: 20px;
            right: -30px;
            background: var(--accent-purple);
            color: white;
            padding: 5px 40px;
            transform: rotate(45deg);
            font-size: 12px;
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .glass-card {
                padding: 1.5rem;
            }
            
            .workflow-line {
                display: none;
            }
        }

        /* 滚动动画 */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .scroll-reveal.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* AI 动画效果 */
        .ai-glow {
            position: relative;
        }

        .ai-glow::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--primary-gradient);
            border-radius: inherit;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
            filter: blur(10px);
        }

        .ai-glow:hover::after {
            opacity: 0.5;
        }

        /* 悬浮按钮 */
        .floating-cta {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
            display: none;
        }

        .floating-cta.show {
            display: block;
            animation: slideInRight 0.5s ease;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 解决方案卡片样式 */
        .solution-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            overflow: hidden;
            height: 451px;
            box-shadow: 0px 12px 32px 0px rgba(6, 1, 43, 0.1);
            transition: all 0.3s ease;
        }

        .solution-card:hover {
            transform: translateY(-5px);
            box-shadow: 0px 20px 40px 0px rgba(6, 1, 43, 0.2);
        }

        .solution-card-header {
            height: 112px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 32px;
            border-radius: 12px 12px 0 0;
        }

        .problem-header {
            background: rgba(255, 255, 255, 0.05);
        }

        .ai-header {
            background: linear-gradient(to right, rgba(114, 112, 255, 0.8), rgba(243, 112, 255, 0.5));
        }

        .solution-card-content {
            padding: 12px 0;
            height: calc(451px - 112px);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .solution-item {
            display: flex;
            align-items: center;
            padding: 24px 40px;
            gap: 12px;
            height: 74px;
        }

        .solution-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .problem-icon {
            background: rgba(208, 71, 71, 0.2);
            color: #d04747;
        }

        .ai-icon {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .solution-text {
            font-size: 18px;
            font-family: 'Noto Sans SC', sans-serif;
            font-weight: 400;
            line-height: normal;
            margin: 0;
            white-space: nowrap;
        }

        .problem-card .solution-text {
            color: rgba(255, 255, 255, 0.6);
        }

        .ai-text {
            color: rgba(255, 255, 255, 0.9);
        }

        /* 响应式处理 */
        @media (max-width: 1024px) {
            .solution-card {
                height: auto;
                min-height: 400px;
            }
            
            .solution-item {
                padding: 20px 24px;
                height: auto;
                min-height: 60px;
            }
            
            .solution-text {
                white-space: normal;
                font-size: 16px;
            }
        }

        @media (max-width: 768px) {
            .solution-card-header {
                height: 80px;
                padding: 0 20px;
            }
            
            .solution-card-header h3 {
                font-size: 20px;
            }
            
            .solution-item {
                padding: 16px 20px;
            }
            
            .solution-text {
                font-size: 14px;
            }
            
            .solution-icon {
                width: 28px;
                height: 28px;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景 -->
    <div class="animated-bg">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar py-4 relative z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-rocket text-2xl" style="color: var(--accent-purple);"></i>
                    <span class="text-xl font-bold" data-lang-key="brand.name">网红营销智能体</span>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#solution" class="text-gray-300 hover:text-white transition" data-lang-key="nav.features">产品功能</a>
                    <a href="#features" class="text-gray-300 hover:text-white transition" data-lang-key="nav.solutions">解决方案</a>
                    <a href="#pricing" class="text-gray-300 hover:text-white transition" data-lang-key="nav.pricing">价格</a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 语言切换 -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center space-x-2 text-gray-300 hover:text-white transition">
                            <i class="fas fa-globe"></i>
                            <span id="currentLang">中文</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div id="languageDropdown" class="absolute top-full right-0 mt-2 bg-gray-800 border border-gray-600 rounded-lg shadow-lg hidden min-w-[140px] z-50">
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="zh">
                                <i class="fas fa-flag mr-2 text-red-400"></i>
                                <span>中文</span>
                            </button>
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="en">
                                <i class="fas fa-flag mr-2 text-blue-400"></i>
                                <span>English</span>
                            </button>
                        </div>
                    </div>
                    
                    <button class="btn-primary" data-lang-key="cta.start" onclick="handleButtonClick()">
                        <i class="fab fa-google"></i>
                        开始建联
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative py-20 overflow-hidden">
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="hero-title mb-6">
                    <span data-lang-key="hero.title">AI 驱动的网红建联管家</span><br>
                    <span class="text-3xl md:text-4xl" data-lang-key="hero.subtitle">一键分析商品、匹配创作者、自动跟进</span>
                </h1>
                
                <p class="text-xl text-gray-300 mb-8" data-lang-key="hero.description">
                    减少 <span class="text-purple-400 font-bold">75%</span> 筛选时间，
                    提升 <span class="text-purple-400 font-bold">5x</span> 回复率，
                    让 AI 接管您的网红营销全流程
                </p>
                
                <div class="flex justify-center mb-12">
                    <button class="btn-primary text-lg px-8 py-4" data-lang-key="cta.startNow" onclick="handleButtonClick()">
                        <i class="fas fa-magic"></i>
                        立即开始建联
                    </button>
                </div>
                
                <!-- 动态演示区域 -->
                <div class="glass-card max-w-3xl mx-auto p-8 ai-glow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full bg-red-500"></div>
                            <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <span class="text-sm text-gray-400">AI Assistant</span>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-link text-purple-400 mt-1"></i>
                            <div class="flex-1">
                                <p class="text-sm text-gray-400 mb-3" data-lang-key="demo.instruction">粘贴商品链接，AI立即开始分析</p>
                                <div class="flex gap-2">
                                    <input type="url" 
                                           placeholder="https://example.com/product..." 
                                           class="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none transition"
                                           id="productUrlInput"
                                           data-lang-key="demo.placeholder">
                                    <button class="btn-primary px-6 py-3 text-sm" onclick="handleButtonClick()" data-lang-key="demo.analyze">
                                        <i class="fas fa-magic mr-1"></i>
                                        分析
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div id="analysisLoader" class="flex items-center justify-center py-4" style="display: none;">
                            <div class="flex space-x-2">
                                <div class="w-2 h-2 rounded-full bg-purple-400 animate-bounce"></div>
                                <div class="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style="animation-delay: 0.1s;"></div>
                                <div class="w-2 h-2 rounded-full bg-purple-400 animate-bounce" style="animation-delay: 0.2s;"></div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <i class="fas fa-users text-3xl text-green-400 mb-2"></i>
                                <p class="text-sm" data-lang-key="demo.result1">匹配 <span class="font-bold">5万+</span> 位博主</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-envelope text-3xl text-blue-400 mb-2"></i>
                                <p class="text-sm" data-lang-key="demo.result2">生成个性化邮件</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-chart-line text-3xl text-purple-400 mb-2"></i>
                                <p class="text-sm" data-lang-key="demo.result3">预测 ROI <span class="font-bold">180%</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 痛点与解决方案 -->
    <section class="py-20 relative" id="solution">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="solution.title">
                    告别传统建联的痛苦
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="solution.subtitle">
                    我们用 AI 解决您的每一个难题
                </p>
            </div>
            
            <!-- 左右对比卡片 -->
            <div class="max-w-7xl mx-auto mb-20">
                <div class="grid lg:grid-cols-2 gap-10 items-start">
                    <!-- 传统方式困扰卡片 -->
                    <div class="solution-card problem-card scroll-reveal">
                        <div class="solution-card-header problem-header">
                            <h3 class="text-2xl font-semibold text-white" data-lang-key="solution.problems.title">
                                传统方式的困扰
                            </h3>
                        </div>
                        <div class="solution-card-content">
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item1">人工查找博主费时费力，效率极低</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item2">邮件模板千篇一律，回复率惨淡</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item3">沟通进度分散，难以追踪管理</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item4">效果难以衡量，ROI 不透明</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI 解决方案卡片 -->
                    <div class="solution-card ai-card scroll-reveal" style="animation-delay: 0.2s;">
                        <div class="solution-card-header ai-header">
                            <h3 class="text-2xl font-semibold text-white" data-lang-key="solution.ai.title">
                                AI 驱动的解决方案
                            </h3>
                        </div>
                        <div class="solution-card-content">
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item1">AI 秒级检索 1万+ 博主库，智能过滤</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-magic"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item2">大模型生成个性化邮件，自动 A/B 迭代</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item3">全流程看板 + 漏斗视图，阶段可视化</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item4">实时 KPI 仪表盘，ROI 一目了然</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 数据对比 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div class="text-center scroll-reveal">
                    <div class="stat-number">120%</div>
                    <p class="text-gray-400" data-lang-key="stats.engagement">互动增长</p>
                </div>
                <div class="text-center scroll-reveal" style="animation-delay: 0.1s;">
                    <div class="stat-number">75%</div>
                    <p class="text-gray-400" data-lang-key="stats.timeSaved">时间节省</p>
                </div>
                <div class="text-center scroll-reveal" style="animation-delay: 0.2s;">
                    <div class="stat-number">5x</div>
                    <p class="text-gray-400" data-lang-key="stats.responseRate">回复率提升</p>
                </div>
                <div class="text-center scroll-reveal" style="animation-delay: 0.3s;">
                    <div class="stat-number">85%</div>
                    <p class="text-gray-400" data-lang-key="stats.accuracy">匹配精准度</p>
                </div>
            </div>
        </div>
    </section>

    <!-- AI 驱动的智能分析 -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="analysis.title">
                    AI 驱动的智能分析
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="analysis.subtitle">
                    深度洞察，让每一次建联都精准有效
                </p>
            </div>
            
            <div class="glass-card max-w-6xl mx-auto p-8">
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- 左侧：概念关系图 -->
                    <div class="relative h-96 flex items-center justify-center">
                        <div class="relative w-80 h-80">
                            <!-- 中心节点 - 绝对居中 -->
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold shadow-2xl z-10" data-lang-key="analysis.core">
                                AI 核心
                            </div>
                            
                            <!-- 周围节点 - 相对于中心精确定位 -->
                            <!-- 左上 -->
                            <div class="absolute top-4 left-4 w-24 h-24 rounded-full bg-blue-500/20 border-2 border-blue-500 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node1">
                                商品分析
                            </div>
                            <!-- 右上 -->
                            <div class="absolute top-4 right-4 w-24 h-24 rounded-full bg-green-500/20 border-2 border-green-500 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node2">
                                博主匹配
                            </div>
                            <!-- 左下 -->
                            <div class="absolute bottom-4 left-4 w-24 h-24 rounded-full bg-orange-500/20 border-2 border-orange-500 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node3">
                                邮件优化
                            </div>
                            <!-- 右下 -->
                            <div class="absolute bottom-4 right-4 w-24 h-24 rounded-full bg-red-500/20 border-2 border-red-500 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node4">
                                效果追踪
                            </div>
                            
                            <!-- 连接线 - 从中心到四个角 -->
                            <svg class="absolute inset-0 w-full h-full" style="z-index: 1;">
                                <!-- 左上线 -->
                                <line x1="50%" y1="50%" x2="20%" y2="20%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                                <!-- 右上线 -->
                                <line x1="50%" y1="50%" x2="80%" y2="20%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                                <!-- 左下线 -->
                                <line x1="50%" y1="50%" x2="20%" y2="80%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                                <!-- 右下线 -->
                                <line x1="50%" y1="50%" x2="80%" y2="80%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                            </svg>
                            

                        </div>
                    </div>
                    
                    <!-- 右侧：关键指标 -->
                    <div class="space-y-6">
                        <h3 class="text-2xl font-semibold mb-4" data-lang-key="analysis.metrics.title">实时性能指标</h3>
                        
                        <div class="space-y-6">
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.accuracy">博主匹配精准度</span>
                                    <span class="text-purple-300 font-bold">85%</span>
                                </div>
                                <div class="w-full bg-gray-800/60 rounded-full h-4 shadow-inner">
                                    <div class="bg-gradient-to-r from-purple-400 to-pink-400 h-4 rounded-full shadow-lg shadow-purple-500/30 transition-all duration-300" style="width: 85%"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.openRate">邮件打开率</span>
                                    <span class="text-blue-300 font-bold">42%</span>
                                </div>
                                <div class="w-full bg-gray-800/60 rounded-full h-4 shadow-inner">
                                    <div class="bg-gradient-to-r from-blue-400 to-cyan-400 h-4 rounded-full shadow-lg shadow-blue-500/30 transition-all duration-300" style="width: 42%"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.conversionRate">回复转化率</span>
                                    <span class="text-green-300 font-bold">28%</span>
                                </div>
                                <div class="w-full bg-gray-800/60 rounded-full h-4 shadow-inner">
                                    <div class="bg-gradient-to-r from-green-400 to-teal-400 h-4 rounded-full shadow-lg shadow-green-500/30 transition-all duration-300" style="width: 28%"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.roi">ROI 表现</span>
                                    <span class="text-yellow-300 font-bold">180%</span>
                                </div>
                                <div class="w-full bg-gray-800/60 rounded-full h-4 shadow-inner relative overflow-hidden">
                                    <div class="bg-gradient-to-r from-yellow-400 to-orange-400 h-4 rounded-full shadow-lg shadow-yellow-500/40 transition-all duration-300 relative" style="width: 100%">
                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 p-4 bg-purple-500/10 rounded-lg border border-purple-500/30">
                            <p class="text-sm" data-lang-key="analysis.metrics.note">
                                <i class="fas fa-lightbulb text-purple-400 mr-2"></i>
                                AI 持续学习优化，每次建联都比上次更精准
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 核心功能 -->
    <section class="py-20 relative" id="features">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="features.title">
                    强大功能，智能高效
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="features.subtitle">
                    六大核心模块，覆盖建联全流程
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                <!-- 功能1: AI商品解析 -->
                <div class="glass-card scroll-reveal">
                    <div class="feature-icon bg-gradient-to-br from-purple-500 to-pink-500">
                        <i class="fas fa-barcode text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature1.title">AI 商品解析</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature1.description">
                        粘贴商品链接，自动抓取标题、图片、规格，秒懂您的产品
                    </p>
                    <div class="flex items-center text-purple-400 text-sm">
                        <i class="fas fa-clock mr-2"></i>
                        <span data-lang-key="feature1.tag">≤30秒完成解析</span>
                    </div>
                </div>
                
                <!-- 功能2: 智能博主匹配 -->
                <div class="glass-card scroll-reveal" style="animation-delay: 0.1s;">
                    <div class="feature-icon bg-gradient-to-br from-blue-500 to-cyan-500">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature2.title">智能博主匹配</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature2.description">
                        相似度算法 + 受众画像筛选，精准找到最合适的创作者
                    </p>
                    <div class="flex items-center text-blue-400 text-sm">
                        <i class="fas fa-bullseye mr-2"></i>
                        <span data-lang-key="feature2.tag">92% 匹配精准度</span>
                    </div>
                </div>
                
                <!-- 功能3: 一键建联 -->
                <div class="glass-card scroll-reveal" style="animation-delay: 0.2s;">
                    <div class="feature-icon bg-gradient-to-br from-green-500 to-teal-500">
                        <i class="fas fa-paper-plane text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature3.title">一键建联</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature3.description">
                        邮件生成、批量发送、意向跟进，全自动化处理
                    </p>
                    <div class="flex items-center text-green-400 text-sm">
                        <i class="fas fa-rocket mr-2"></i>
                        <span data-lang-key="feature3.tag">45%+ 平均回复率</span>
                    </div>
                </div>
                
                <!-- 功能4: 多邮箱管理 -->
                <div class="glass-card scroll-reveal" style="animation-delay: 0.3s;">
                    <div class="feature-icon bg-gradient-to-br from-yellow-500 to-orange-600">
                        <i class="fas fa-envelope-open-text text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature4.title">多邮箱 & Gmail OAuth</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature4.description">
                        支持 QQ/163/自定义 SMTP，安全便捷的邮箱管理
                    </p>
                    <div class="flex items-center text-orange-400 text-sm">
                        <i class="fas fa-envelope mr-2"></i>
                        <span data-lang-key="feature4.tag">多平台邮箱支持</span>
                    </div>
                </div>
                
                <!-- 功能5: 仪表盘 -->
                <div class="glass-card scroll-reveal" style="animation-delay: 0.4s;">
                    <div class="feature-icon bg-gradient-to-br from-indigo-500 to-purple-500">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature5.title">全流程仪表盘</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature5.description">
                        活跃合作、回复率、转化漏斗，数据驱动决策
                    </p>
                    <div class="flex items-center text-indigo-400 text-sm">
                        <i class="fas fa-eye mr-2"></i>
                        <span data-lang-key="feature5.tag">实时数据更新</span>
                    </div>
                </div>
                
                <!-- 功能6: 智能优化 -->
                <div class="glass-card scroll-reveal" style="animation-delay: 0.5s;">
                    <div class="feature-icon bg-gradient-to-br from-emerald-500 to-teal-500">
                        <i class="fas fa-brain text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature6.title">AI 智能优化</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature6.description">
                        机器学习持续优化邮件模板，动态调整发送策略
                    </p>
                    <div class="flex items-center text-emerald-400 text-sm">
                        <i class="fas fa-rocket mr-2"></i>
                        <span data-lang-key="feature6.tag">持续自动优化</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 工作流程 -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="workflow.title">
                    简单五步，开启智能建联
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="workflow.subtitle">
                    从注册到成功合作，AI 全程陪伴
                </p>
            </div>
            
            <div class="relative max-w-5xl mx-auto">
                <div class="workflow-line hidden md:block"></div>
                
                <div class="grid md:grid-cols-5 gap-6">
                    <!-- 步骤1 -->
                    <div class="text-center scroll-reveal">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            1
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step1.title">创建账户</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step1.description">快速注册开始</p>
                    </div>
                    
                    <!-- 步骤2 -->
                    <div class="text-center scroll-reveal" style="animation-delay: 0.1s;">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            2
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step2.title">粘贴商品</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step2.description">链接或上传商品</p>
                    </div>
                    
                    <!-- 步骤3 -->
                    <div class="text-center scroll-reveal" style="animation-delay: 0.2s;">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-green-500 to-teal-500 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            3
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step3.title">AI 匹配</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step3.description">智能推荐博主</p>
                    </div>
                    
                    <!-- 步骤4 -->
                    <div class="text-center scroll-reveal" style="animation-delay: 0.3s;">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-yellow-500 to-orange-600 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            4
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step4.title">发送邮件</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step4.description">个性化触达</p>
                    </div>
                    
                    <!-- 步骤5 -->
                    <div class="text-center scroll-reveal" style="animation-delay: 0.4s;">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-indigo-500 to-purple-500 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            5
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step5.title">追踪优化</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step5.description">效果实时监控</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <button class="btn-primary text-lg" data-lang-key="cta.startNow" onclick="handleButtonClick()">
                    <i class="fas fa-rocket mr-2"></i>
                    立即开始建联
                </button>
            </div>
        </div>
    </section>

    <!-- 社会证明 -->
    <section class="py-20 relative bg-gradient-to-b from-transparent to-purple-900/10">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="social.title">
                    深受品牌信赖
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="social.subtitle">
                    已帮助 <span class="text-purple-400 font-bold">500+</span> 品牌成功出海
                </p>
            </div>
            
            <!-- 客户Logo -->
            <div class="flex flex-wrap justify-center items-center gap-8 mb-16">
                <div class="w-36 h-20 bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl flex items-center justify-center hover:border-purple-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-purple-300 font-semibold">CreativeFlow</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl flex items-center justify-center hover:border-blue-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-blue-300 font-semibold">InfluenceHub</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-green-500/10 to-teal-500/10 border border-green-500/20 rounded-xl flex items-center justify-center hover:border-green-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-green-300 font-semibold">TrendMaker</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-xl flex items-center justify-center hover:border-orange-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-orange-300 font-semibold">SocialPeak</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-pink-500/10 to-purple-500/10 border border-pink-500/20 rounded-xl flex items-center justify-center hover:border-pink-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-pink-300 font-semibold">ViralCraft</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-indigo-500/20 rounded-xl flex items-center justify-center hover:border-indigo-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-indigo-300 font-semibold">BrandBoost</span>
                </div>
            </div>
            
            <!-- 客户案例 -->
            <div class="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
                <div class="glass-card scroll-reveal">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 mr-3 flex items-center justify-center">
                            <i class="fas fa-user-tie text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" data-lang-key="testimonials.user1.name">张经理</h4>
                            <p class="text-sm text-gray-400" data-lang-key="testimonials.user1.title">跨境电商创始人</p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic" data-lang-key="testimonials.user1.quote">
                        "首次使用就带来了 <span class="text-purple-400 font-bold">6位数</span> 的销售额！
                        AI 匹配的博主质量远超预期。"
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                
                <div class="glass-card scroll-reveal" style="animation-delay: 0.1s;">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 mr-3 flex items-center justify-center">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" data-lang-key="testimonials.user2.name">李总监</h4>
                            <p class="text-sm text-gray-400" data-lang-key="testimonials.user2.title">品牌营销负责人</p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic" data-lang-key="testimonials.user2.quote">
                        "团队效率提升 <span class="text-purple-400 font-bold">200%</span>，
                        再也不用熬夜找博主了！"
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                
                <div class="glass-card scroll-reveal" style="animation-delay: 0.2s;">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-teal-500 mr-3 flex items-center justify-center">
                            <i class="fas fa-crown text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" data-lang-key="testimonials.user3.name">王CEO</h4>
                            <p class="text-sm text-gray-400" data-lang-key="testimonials.user3.title">DTC品牌创始人</p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic" data-lang-key="testimonials.user3.quote">
                        "ROI 达到 <span class="text-purple-400 font-bold">180%</span>，
                        这是我们做过最成功的营销投资！"
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格方案 -->
    <section class="py-20 relative" id="pricing">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="pricing.title">
                    透明定价，按需选择
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="pricing.subtitle">
                    零前期成本，只为结果付费
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
                <!-- Starter -->
                <div class="glass-card pricing-card scroll-reveal flex flex-col">
                    <h3 class="text-2xl font-bold mb-2" data-lang-key="pricing.starter.name">Starter</h3>
                    <p class="text-gray-400 mb-4" data-lang-key="pricing.starter.description">适合个人和小团队</p>
                    <div class="text-4xl font-bold mb-4" data-lang-key="pricing.starter.price">
                        免费
                    </div>
                    <ul class="space-y-3 mb-6 flex-grow">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature1">月度 50 封建联</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature2">基础博主匹配</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature3">单人席位</span>
                        </li>
                        <li class="flex items-center text-gray-500">
                            <i class="fas fa-times text-gray-600 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature4">高级数据分析</span>
                        </li>
                    </ul>
                    <button class="w-full py-3 border border-gray-600 rounded-full hover:border-purple-500 transition text-center" data-lang-key="pricing.starter.button" onclick="handleButtonClick()">
                        开始使用
                    </button>
                </div>
                
                <!-- Pro -->
                <div class="glass-card pricing-card featured scroll-reveal flex flex-col" style="animation-delay: 0.1s;">
                    <h3 class="text-2xl font-bold mb-2" data-lang-key="pricing.pro.name">Pro</h3>
                    <p class="text-gray-400 mb-4" data-lang-key="pricing.pro.description">团队小规模投放</p>
                    <div class="text-4xl font-bold mb-4" data-lang-key="pricing.pro.price">
                        ¥199<span class="text-lg font-normal">/月</span>
                    </div>
                    <ul class="space-y-3 mb-6 flex-grow">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature1">月度 1000 封建联</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature2">高级筛选功能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature3">5 人团队协作</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature4">实时数据分析</span>
                        </li>
                    </ul>
                    <button class="btn-primary w-full py-3 justify-center" data-lang-key="pricing.pro.button" onclick="handleButtonClick()">
                        立即升级
                    </button>
                </div>
                
                <!-- Enterprise -->
                <div class="glass-card pricing-card scroll-reveal flex flex-col" style="animation-delay: 0.2s;">
                    <h3 class="text-2xl font-bold mb-2" data-lang-key="pricing.enterprise.name">Enterprise</h3>
                    <p class="text-gray-400 mb-4" data-lang-key="pricing.enterprise.description">代理商和大品牌</p>
                    <div class="text-4xl font-bold mb-4" data-lang-key="pricing.enterprise.price">
                        定制
                    </div>
                    <ul class="space-y-3 mb-6 flex-grow">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature1">不限建联数量</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature2">不限团队席位</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature3">API 接入</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature4">专属客户经理</span>
                        </li>
                    </ul>
                    <button class="w-full py-3 border border-gray-600 rounded-full hover:border-purple-500 transition text-center" data-lang-key="pricing.enterprise.button" onclick="handleButtonClick()">
                        联系销售
                    </button>
                </div>
            </div>
            
            <div class="text-center mt-8">
                <p class="text-purple-400" data-lang-key="pricing.note">
                    <i class="fas fa-info-circle mr-2"></i>
                    仅在成功合作时抽佣 5%，无隐藏费用
                </p>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="faq.title">
                    常见问题
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="faq.subtitle">
                    我们为您解答所有疑问
                </p>
            </div>
            
            <div class="max-w-3xl mx-auto space-y-4">
                <div class="glass-card scroll-reveal">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q1.question">Google 登录安全吗？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q1.answer">
                            我们使用 OAuth 2.0 标准认证，不会存储您的密码。所有数据传输均采用 SSL 加密，
                            符合 GDPR 和 ISO-27001 安全标准。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal" style="animation-delay: 0.1s;">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q2.question">数据源从哪里来？多久更新？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q2.answer">
                            我们整合了 YouTube、Instagram、TikTok 等主流平台的公开数据，
                            拥有 5万+ 活跃博主库，每周更新，确保数据新鲜度。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal" style="animation-delay: 0.2s;">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q3.question">如何保证博主真实有效？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q3.answer">
                            AI 会分析博主的互动率、粉丝增长曲线、内容质量等多维度数据，
                            自动过滤虚假账号，确保匹配的都是高质量创作者。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal" style="animation-delay: 0.3s;">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q4.question">是否支持 TikTok / Instagram？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q4.answer">
                            支持！我们覆盖 YouTube、TikTok、Instagram、Twitter 等主流平台，
                            未来还将接入更多平台，满足您的全渠道营销需求。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal" style="animation-delay: 0.4s;">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q5.question">AI 匹配效果如何保证？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q5.answer">
                            AI 会持续学习和优化匹配算法，结合历史合作数据、受众重合度、
                            内容风格匹配等多个维度，确保推荐精准度不断提升。
                        </p>
                    </details>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="glass-card max-w-4xl mx-auto text-center p-12 ai-glow">
                <h2 class="text-3xl md:text-4xl font-bold mb-6" data-lang-key="final.cta.title">
                    准备好革新您的网红营销了吗？
                </h2>
                <p class="text-xl text-gray-300 mb-8" data-lang-key="final.cta.description">
                    加入 500+ 成功品牌，让 AI 为您的出海之路保驾护航
                </p>
                <div class="flex justify-center">
                    <button class="btn-primary text-lg px-8 py-4" data-lang-key="cta.startNow" onclick="handleButtonClick()">
                        <i class="fab fa-google mr-2"></i>
                        立即开始建联
                    </button>
                </div>
                <p class="mt-6 text-sm text-gray-400" data-lang-key="final.cta.note">
                    <i class="fas fa-shield-alt mr-2"></i>
                    无需信用卡 · 免费试用 · 随时取消
                </p>
            </div>
        </div>
    </section>



    <!-- 悬浮CTA按钮 -->
    <div class="floating-cta">
        <button class="btn-primary shadow-2xl" data-lang-key="cta.start" onclick="handleButtonClick()">
            <i class="fas fa-rocket mr-2"></i>
            开始建联
        </button>
    </div>

    <script>
        // 统一跳转地址
        const REDIRECT_URL = 'https://demo.leadcoins.club';

        // 统一按钮处理函数 - 在当前页面跳转
        function handleButtonClick() {
            window.location.href = REDIRECT_URL;
        }

        // 多语言数据
        const languages = {
            zh: {
                // 页面基本信息
                'page.title': '网红营销智能体 - AI驱动的网红建联管家',
                'brand.name': '网红营销智能体',
                
                // 导航
                'nav.features': '产品功能',
                'nav.solutions': '解决方案',
                'nav.pricing': '价格',
                'cta.start': '开始建联',
                'cta.startNow': '立即开始建联',
                
                // Hero 部分
                'hero.title': 'AI 驱动的网红建联管家',
                'hero.subtitle': '一键分析商品、匹配创作者、自动跟进',
                'hero.description': '减少 <span class="text-purple-400 font-bold">75%</span> 筛选时间，提升 <span class="text-purple-400 font-bold">5x</span> 回复率，让 AI 接管您的网红营销全流程',
                
                // 解决方案
                'solution.title': '告别传统建联的痛苦',
                'solution.subtitle': '我们用 AI 解决您的每一个难题',
                
                // 功能
                'features.title': '强大功能，智能高效',
                'features.subtitle': '六大核心模块，覆盖建联全流程',
                'feature1.title': 'AI 商品解析',
                'feature1.description': '粘贴商品链接，自动抓取标题、图片、规格，秒懂您的产品',
                'feature2.title': '智能博主匹配',
                'feature2.description': '相似度算法 + 受众画像筛选，精准找到最合适的创作者',
                'feature3.title': '一键建联',
                'feature3.description': '邮件生成、批量发送、意向跟进，全自动化处理',
                'feature1.tag': '≤30秒完成解析',
                'feature2.tag': '92% 匹配精准度',
                'feature3.tag': '45%+ 平均回复率',
                
                // 价格
                'pricing.title': '透明定价，按需选择',
                'pricing.subtitle': '零前期成本，只为结果付费',
                'pricing.starter.name': 'Starter',
                'pricing.starter.description': '适合个人和小团队',
                'pricing.starter.price': '免费',
                'pricing.pro.name': 'Pro',
                'pricing.pro.description': '团队小规模投放',
                'pricing.pro.price': '¥199<span class="text-lg font-normal">/月</span>',
                'pricing.enterprise.name': 'Enterprise',
                'pricing.enterprise.description': '代理商和大品牌',
                'pricing.enterprise.price': '定制',
                'pricing.starter.button': '开始使用',
                'pricing.pro.button': '立即升级',
                'pricing.enterprise.button': '联系销售',
                'pricing.note': '仅在成功合作时抽佣 5%，无隐藏费用',
                
                // FAQ
                'faq.title': '常见问题',
                'faq.subtitle': '我们为您解答所有疑问',
                
                // 演示区域
                'demo.instruction': '粘贴商品链接，AI立即开始分析',
                'demo.placeholder': 'https://example.com/product...',
                'demo.analyze': '分析',
                'demo.result1': '匹配 <span class="font-bold">5万+</span> 位博主',
                'demo.result2': '生成个性化邮件',
                'demo.result3': '预测 ROI <span class="font-bold">180%</span>',
                
                // AI 智能分析
                'analysis.title': 'AI 驱动的智能分析',
                'analysis.subtitle': '深度洞察，让每一次建联都精准有效',
                
                // 工作流程
                'workflow.title': '简单五步，开启智能建联',
                'workflow.subtitle': '从注册到成功合作，AI 全程陪伴',
                'workflow.step1.title': '创建账户',
                'workflow.step1.description': '快速注册开始',
                'workflow.step2.title': '粘贴商品',
                'workflow.step2.description': '链接或上传商品',
                'workflow.step3.title': 'AI 匹配',
                'workflow.step3.description': '智能推荐博主',
                'workflow.step4.title': '发送邮件',
                'workflow.step4.description': '个性化触达',
                'workflow.step5.title': '追踪优化',
                'workflow.step5.description': '效果实时监控',
                
                // 社会证明
                'social.title': '深受品牌信赖',
                'social.subtitle': '已帮助 <span class="text-purple-400 font-bold">500+</span> 品牌成功出海',
                
                // 解决方案痛点
                'solution.problems.title': '传统方式的困扰',
                'solution.problems.item1': '人工查找博主费时费力，效率极低',
                'solution.problems.item2': '邮件模板千篇一律，回复率惨淡',
                'solution.problems.item3': '沟通进度分散，难以追踪管理',
                'solution.problems.item4': '效果难以衡量，ROI 不透明',
                'solution.ai.title': 'AI 驱动的解决方案',
                'solution.ai.item1': 'AI 秒级检索 30万+ 博主库，智能过滤',
                'solution.ai.item2': '大模型生成个性化邮件，自动 A/B 迭代',
                'solution.ai.item3': '全流程看板 + 漏斗视图，阶段可视化',
                'solution.ai.item4': '实时 KPI 仪表盘，ROI 一目了然',
                
                // 统计数据
                'stats.engagement': '互动增长',
                'stats.timeSaved': '时间节省',
                'stats.responseRate': '回复率提升',
                'stats.accuracy': '匹配精准度',
                
                // AI分析节点
                'analysis.core': 'AI 核心',
                'analysis.node1': '商品分析',
                'analysis.node2': '博主匹配',
                'analysis.node3': '邮件优化',
                'analysis.node4': '效果追踪',
                'analysis.metrics.title': '实时性能指标',
                'analysis.metrics.accuracy': '博主匹配精准度',
                'analysis.metrics.openRate': '邮件打开率',
                'analysis.metrics.conversionRate': '回复转化率',
                'analysis.metrics.roi': 'ROI 表现',
                'analysis.metrics.note': 'AI 持续学习优化，每次建联都比上次更精准',
                
                // 功能卡片
                'feature4.title': '多邮箱 & Gmail OAuth',
                'feature4.description': '支持 QQ/163/自定义 SMTP，安全便捷的邮箱管理',
                'feature4.tag': '多平台邮箱支持',
                'feature5.title': '全流程仪表盘',
                'feature5.description': '活跃合作、回复率、转化漏斗，数据驱动决策',
                'feature5.tag': '实时数据更新',
                'feature6.title': 'AI 智能优化',
                'feature6.description': '机器学习持续优化邮件模板，动态调整发送策略',
                'feature6.tag': '持续自动优化',
                
                // 客户案例
                'testimonials.user1.name': '张经理',
                'testimonials.user1.title': '跨境电商创始人',
                'testimonials.user1.quote': '"首次使用就带来了 <span class="text-purple-400 font-bold">6位数</span> 的销售额！AI 匹配的博主质量远超预期。"',
                'testimonials.user2.name': '李总监',
                'testimonials.user2.title': '品牌营销负责人',
                'testimonials.user2.quote': '"团队效率提升 <span class="text-purple-400 font-bold">200%</span>，再也不用熬夜找博主了！"',
                'testimonials.user3.name': '王CEO',
                'testimonials.user3.title': 'DTC品牌创始人',
                'testimonials.user3.quote': '"ROI 达到 <span class="text-purple-400 font-bold">180%</span>，这是我们做过最成功的营销投资！"',
                
                // 价格功能列表
                'pricing.starter.feature1': '月度 50 封建联',
                'pricing.starter.feature2': '基础博主匹配',
                'pricing.starter.feature3': '单人席位',
                'pricing.starter.feature4': '高级数据分析',
                'pricing.pro.feature1': '月度 1000 封建联',
                'pricing.pro.feature2': '高级筛选功能',
                'pricing.pro.feature3': '5 人团队协作',
                'pricing.pro.feature4': '实时数据分析',
                'pricing.enterprise.feature1': '不限建联数量',
                'pricing.enterprise.feature2': '不限团队席位',
                'pricing.enterprise.feature3': 'API 接入',
                'pricing.enterprise.feature4': '专属客户经理',
                
                // FAQ
                'faq.q1.question': 'Google 登录安全吗？',
                'faq.q1.answer': '我们使用 OAuth 2.0 标准认证，不会存储您的密码。所有数据传输均采用 SSL 加密，符合 GDPR 和 ISO-27001 安全标准。',
                'faq.q2.question': '数据源从哪里来？多久更新？',
                'faq.q2.answer': '我们整合了 YouTube、Instagram、TikTok 等主流平台的公开数据，拥有 5万+ 活跃博主库，每周更新，确保数据新鲜度。',
                'faq.q3.question': '如何保证博主真实有效？',
                'faq.q3.answer': 'AI 会分析博主的互动率、粉丝增长曲线、内容质量等多维度数据，自动过滤虚假账号，确保匹配的都是高质量创作者。',
                'faq.q4.question': '是否支持 TikTok / Instagram？',
                'faq.q4.answer': '支持！我们覆盖 YouTube、TikTok、Instagram、Twitter 等主流平台，未来还将接入更多平台，满足您的全渠道营销需求。',
                'faq.q5.question': 'AI 匹配效果如何保证？',
                'faq.q5.answer': 'AI 会持续学习和优化匹配算法，结合历史合作数据、受众重合度、内容风格匹配等多个维度，确保推荐精准度不断提升。',
                
                // 最终 CTA
                'final.cta.title': '准备好革新您的网红营销了吗？',
                'final.cta.description': '加入 500+ 成功品牌，让 AI 为您的出海之路保驾护航',
                'final.cta.note': '无需信用卡 · 免费试用 · 随时取消',
            },
            en: {
                // 页面基本信息
                'page.title': 'Cross-Border Assistant - AI-Powered Influencer Outreach Manager',
                'brand.name': 'Cross-Border Assistant',
                
                // 导航
                'nav.features': 'Features',
                'nav.solutions': 'Solutions',
                'nav.pricing': 'Pricing',
                'cta.start': 'Start Connecting',
                'cta.startNow': 'Start Now',
                
                // Hero 部分
                'hero.title': 'AI-Powered Influencer Outreach Assistant',
                'hero.subtitle': 'Analyze Products, Match Creators, Auto Follow-up in One Click',
                'hero.description': 'Reduce <span class="text-purple-400 font-bold">75%</span> screening time, increase <span class="text-purple-400 font-bold">5x</span> response rate, let AI take over your influencer marketing workflow',
                
                // 解决方案
                'solution.title': 'Say Goodbye to Traditional Outreach Pain',
                'solution.subtitle': 'We use AI to solve every challenge',
                
                // 功能
                'features.title': 'Powerful Features, Smart & Efficient',
                'features.subtitle': 'Six core modules covering the entire outreach process',
                'feature1.title': 'AI Product Analysis',
                'feature1.description': 'Paste product links, automatically extract titles, images, specifications, understand your product instantly',
                'feature2.title': 'Smart Influencer Matching',
                'feature2.description': 'Similarity algorithms + audience profiling filters to precisely find the most suitable creators',
                'feature3.title': 'One-Click Outreach',
                'feature3.description': 'Email generation, batch sending, intention follow-up, fully automated processing',
                'feature1.tag': '≤30s completion',
                'feature2.tag': '92% matching accuracy',
                'feature3.tag': '45%+ average response rate',
                
                // 价格
                'pricing.title': 'Transparent Pricing, Choose What You Need',
                'pricing.subtitle': 'Zero upfront cost, pay only for results',
                'pricing.starter.name': 'Starter',
                'pricing.starter.description': 'Perfect for individuals and small teams',
                'pricing.starter.price': 'Free',
                'pricing.pro.name': 'Pro',
                'pricing.pro.description': 'For team small-scale campaigns',
                'pricing.pro.price': '$29<span class="text-lg font-normal">/month</span>',
                'pricing.enterprise.name': 'Enterprise',
                'pricing.enterprise.description': 'For agencies and large brands',
                'pricing.enterprise.price': 'Custom',
                'pricing.starter.button': 'Get Started',
                'pricing.pro.button': 'Upgrade Now',
                'pricing.enterprise.button': 'Contact Sales',
                'pricing.note': 'Only 5% commission on successful collaborations, no hidden fees',
                
                // FAQ
                'faq.title': 'Frequently Asked Questions',
                'faq.subtitle': 'We answer all your questions',
                
                // 演示区域
                'demo.instruction': 'Paste product link, AI starts analysis immediately',
                'demo.placeholder': 'https://example.com/product...',
                'demo.analyze': 'Analyze',
                'demo.result1': 'Match <span class="font-bold">50,000+</span> influencers',
                'demo.result2': 'Generate personalized emails',
                'demo.result3': 'Predict ROI <span class="font-bold">180%</span>',
                
                // AI 智能分析
                'analysis.title': 'AI-Driven Smart Analysis',
                'analysis.subtitle': 'Deep insights make every outreach precise and effective',
                
                // 工作流程
                'workflow.title': 'Simple 5 Steps to Smart Outreach',
                'workflow.subtitle': 'From registration to successful collaboration, AI accompanies you all the way',
                'workflow.step1.title': 'Create Account',
                'workflow.step1.description': 'Quick registration to start',
                'workflow.step2.title': 'Paste Product',
                'workflow.step2.description': 'Link or upload product',
                'workflow.step3.title': 'AI Matching',
                'workflow.step3.description': 'Smart influencer recommendations',
                'workflow.step4.title': 'Send Emails',
                'workflow.step4.description': 'Personalized outreach',
                'workflow.step5.title': 'Track & Optimize',
                'workflow.step5.description': 'Real-time performance monitoring',
                
                // 社会证明
                'social.title': 'Trusted by Brands',
                'social.subtitle': 'Helped <span class="text-purple-400 font-bold">500+</span> brands succeed globally',
                
                // 解决方案痛点
                'solution.problems.title': 'Traditional Outreach Pain Points',
                'solution.problems.item1': 'Manual influencer search is time-consuming and inefficient',
                'solution.problems.item2': 'Generic email templates lead to poor response rates',
                'solution.problems.item3': 'Scattered communication progress, difficult to track',
                'solution.problems.item4': 'Hard to measure effectiveness, unclear ROI',
                'solution.ai.title': 'AI-Driven Solutions',
                'solution.ai.item1': 'AI searches 10,000+ influencer database in seconds with smart filtering',
                'solution.ai.item2': 'Large language models generate personalized emails with automatic A/B testing',
                'solution.ai.item3': 'Full-process kanban + funnel view for stage visualization',
                'solution.ai.item4': 'Real-time KPI dashboard, ROI at a glance',
                
                // 统计数据
                'stats.engagement': 'Engagement Growth',
                'stats.timeSaved': 'Time Saved',
                'stats.responseRate': 'Response Rate Boost',
                'stats.accuracy': 'Matching Accuracy',
                
                // AI分析节点
                'analysis.core': 'AI Core',
                'analysis.node1': 'Product Analysis',
                'analysis.node2': 'Influencer Matching',
                'analysis.node3': 'Email Optimization',
                'analysis.node4': 'Performance Tracking',
                'analysis.metrics.title': 'Real-time Performance Metrics',
                'analysis.metrics.accuracy': 'Influencer Matching Accuracy',
                'analysis.metrics.openRate': 'Email Open Rate',
                'analysis.metrics.conversionRate': 'Reply Conversion Rate',
                'analysis.metrics.roi': 'ROI Performance',
                'analysis.metrics.note': 'AI continuously learns and optimizes, making each outreach more precise than the last',
                
                // 功能卡片
                'feature4.title': 'Multi-Email & Gmail OAuth',
                'feature4.description': 'Support QQ/163/custom SMTP, secure and convenient email management',
                'feature4.tag': 'Multi-platform email support',
                'feature5.title': 'Full-Process Dashboard',
                'feature5.description': 'Active collaborations, response rates, conversion funnels, data-driven decisions',
                'feature5.tag': 'Real-time data updates',
                'feature6.title': 'AI Smart Optimization',
                'feature6.description': 'Machine learning continuously optimizes email templates, dynamically adjusts sending strategies',
                'feature6.tag': 'Continuous auto optimization',
                
                // 客户案例
                'testimonials.user1.name': 'Manager Zhang',
                'testimonials.user1.title': 'Cross-border E-commerce Founder',
                'testimonials.user1.quote': '"First use brought <span class="text-purple-400 font-bold">6-figure</span> sales! AI-matched influencer quality exceeded expectations."',
                'testimonials.user2.name': 'Director Li',
                'testimonials.user2.title': 'Brand Marketing Director',
                'testimonials.user2.quote': '"Team efficiency improved by <span class="text-purple-400 font-bold">200%</span>, no more staying up late to find influencers!"',
                'testimonials.user3.name': 'CEO Wang',
                'testimonials.user3.title': 'DTC Brand Founder',
                'testimonials.user3.quote': '"ROI reached <span class="text-purple-400 font-bold">180%</span>, this is our most successful marketing investment!"',
                
                // 价格功能列表
                'pricing.starter.feature1': '50 outreach emails per month',
                'pricing.starter.feature2': 'Basic influencer matching',
                'pricing.starter.feature3': 'Single user seat',
                'pricing.starter.feature4': 'Advanced data analytics',
                'pricing.pro.feature1': '1,000 outreach emails per month',
                'pricing.pro.feature2': 'Advanced filtering features',
                'pricing.pro.feature3': '5-person team collaboration',
                'pricing.pro.feature4': 'Real-time data analytics',
                'pricing.enterprise.feature1': 'Unlimited outreach emails',
                'pricing.enterprise.feature2': 'Unlimited team seats',
                'pricing.enterprise.feature3': 'API access',
                'pricing.enterprise.feature4': 'Dedicated account manager',
                
                // FAQ
                'faq.q1.question': 'Is Google login secure?',
                'faq.q1.answer': 'We use OAuth 2.0 standard authentication and do not store your password. All data transmission uses SSL encryption, compliant with GDPR and ISO-27001 security standards.',
                'faq.q2.question': 'Where does the data come from? How often is it updated?',
                'faq.q2.answer': 'We integrate public data from mainstream platforms like YouTube, Instagram, TikTok, with 50,000+ active influencer database updated weekly to ensure data freshness.',
                'faq.q3.question': 'How do you ensure influencers are authentic and effective?',
                'faq.q3.answer': 'AI analyzes multi-dimensional data including engagement rates, follower growth curves, content quality to automatically filter fake accounts, ensuring matched creators are high-quality.',
                'faq.q4.question': 'Do you support TikTok / Instagram?',
                'faq.q4.answer': 'Yes! We cover mainstream platforms like YouTube, TikTok, Instagram, Twitter, with more platforms to be integrated to meet your omnichannel marketing needs.',
                'faq.q5.question': 'How do you guarantee AI matching effectiveness?',
                'faq.q5.answer': 'AI continuously learns and optimizes matching algorithms, combining historical collaboration data, audience overlap, content style matching across multiple dimensions to ensure recommendation accuracy keeps improving.',
                
                // 最终 CTA
                'final.cta.title': 'Ready to revolutionize your influencer marketing?',
                'final.cta.description': 'Join 500+ successful brands, let AI power your global expansion journey',
                'final.cta.note': 'No credit card required · Free trial · Cancel anytime',
            }
        };

        // 当前语言
        let currentLanguage = 'zh';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            
            // 更新所有带有 data-lang-key 的元素
            document.querySelectorAll('[data-lang-key]').forEach(element => {
                const key = element.getAttribute('data-lang-key');
                if (languages[lang] && languages[lang][key]) {
                    // 特殊处理 title 标签
                    if (element.tagName === 'TITLE') {
                        element.textContent = languages[lang][key];
                    }
                    // 特殊处理 input 的 placeholder
                    else if (element.tagName === 'INPUT' && element.hasAttribute('placeholder')) {
                        element.placeholder = languages[lang][key];
                    }
                    // 普通元素使用 innerHTML
                    else {
                        element.innerHTML = languages[lang][key];
                    }
                }
            });
            
            // 更新语言选择器显示
            const currentLangElement = document.getElementById('currentLang');
            currentLangElement.textContent = lang === 'zh' ? '中文' : 'English';
            
            // 更新下拉菜单中的选中状态
            document.querySelectorAll('.language-option').forEach(option => {
                const optionLang = option.getAttribute('data-lang');
                if (optionLang === lang) {
                    option.classList.add('bg-gray-700');
                } else {
                    option.classList.remove('bg-gray-700');
                }
            });
            
            // 更新 HTML lang 属性
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            // 保存语言偏好到本地存储
            localStorage.setItem('preferred-language', lang);
            
            // 隐藏下拉菜单
            document.getElementById('languageDropdown').classList.add('hidden');
        }

        // 检测系统语言
        function detectSystemLanguage() {
            const systemLang = navigator.language || navigator.userLanguage;
            // 检查是否为中文相关语言
            if (systemLang.startsWith('zh')) {
                return 'zh';
            }
            // 默认英文
            return 'en';
        }

        // 初始化语言
        function initLanguage() {
            // 优先级：本地存储 > 系统语言 > 默认中文
            const savedLanguage = localStorage.getItem('preferred-language');
            if (savedLanguage && languages[savedLanguage]) {
                switchLanguage(savedLanguage);
            } else {
                // 根据系统语言自动选择
                const systemLanguage = detectSystemLanguage();
                switchLanguage(systemLanguage);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initLanguage();
            
            // 语言切换按钮事件
            const languageToggle = document.getElementById('languageToggle');
            const languageDropdown = document.getElementById('languageDropdown');
            
            languageToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                languageDropdown.classList.toggle('hidden');
            });
            
            // 语言选项点击事件
            document.querySelectorAll('.language-option').forEach(option => {
                option.addEventListener('click', function() {
                    const lang = this.getAttribute('data-lang');
                    switchLanguage(lang);
                });
            });
            
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function() {
                languageDropdown.classList.add('hidden');
            });
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 滚动显示动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });

        // 悬浮CTA按钮显示逻辑
        const floatingCTA = document.querySelector('.floating-cta');
        window.addEventListener('scroll', function() {
            if (window.scrollY > 800) {
                floatingCTA.classList.add('show');
            } else {
                floatingCTA.classList.remove('show');
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // FAQ 展开/收起动画
        document.querySelectorAll('details').forEach(detail => {
            detail.addEventListener('toggle', function() {
                const icon = this.querySelector('.fa-chevron-down');
                if (this.open) {
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        });

        // 数字动画
        function animateValue(element, start, end, duration) {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                element.textContent = Math.floor(progress * (end - start) + start) + '%';
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        }

        // 当统计数字进入视口时触发动画
        const statObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting && !entry.target.animated) {
                    entry.target.animated = true;
                    const text = entry.target.textContent;
                    const match = text.match(/(\d+)/);
                    if (match) {
                        const endValue = parseInt(match[1]);
                        const suffix = text.replace(match[0], '');
                        entry.target.textContent = '0' + suffix;
                        setTimeout(() => {
                            animateValue(entry.target, 0, endValue, 2000);
                        }, 200);
                    }
                }
            });
        }, { threshold: 0.5 });

        document.querySelectorAll('.stat-number').forEach(el => {
            statObserver.observe(el);
        });


    </script>

    <!-- 
    按钮配置说明:
    1. 所有按钮都会直接跳转到统一地址: https://demo.leadcoins.club
    2. 包括导航栏、Hero区域、定价、分析等所有按钮
    3. 点击后直接在当前页面跳转到目标地址，无需确认
    4. 如需修改跳转地址，请修改 REDIRECT_URL 变量
    -->
</body>
</html>